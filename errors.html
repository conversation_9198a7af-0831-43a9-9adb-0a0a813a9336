<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flora Net - خطأ</title>
  <style>
    /* تحميل خط الألمراي */
    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
    }

    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
    }

    /* إعادة تعيين الأنماط الأساسية */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* تدرج خلفية متحرك */
    body {
      font-family: 'Almarai', 'Tahoma', sans-serif;
      background: linear-gradient(-45deg, #ff6b6b, #ee5a52, #ff8a80, #ffab91);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow-x: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* الحاوية الرئيسية */
    .error-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      padding: 40px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInUp 0.8s ease-out;
      position: relative;
      overflow: hidden;
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
      text-align: center;
    }

    .error-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ff6b6b, #ee5a52);
      border-radius: 25px 25px 0 0;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* الشعار */
    .logo {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin: 0 auto 25px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      display: block;
      border: 3px solid rgba(255, 255, 255, 0.8);
    }

    @media (min-width: 768px) {
      .logo {
        width: 120px;
        height: 120px;
        margin-bottom: 30px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        border-width: 4px;
      }
    }

    @media (min-width: 992px) {
      .logo {
        width: 140px;
        height: 140px;
        margin-bottom: 35px;
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
        border-width: 5px;
      }
    }

    .logo:hover {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 30px 70px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 1);
    }

    /* أيقونة الخطأ */
    .error-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 25px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: errorShake 2s ease-in-out infinite;
      box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    }

    @keyframes errorShake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-3px); }
      75% { transform: translateX(3px); }
    }

    .error-icon svg {
      width: 40px;
      height: 40px;
      fill: white;
    }

    /* العنوان */
    .error-title {
      font-size: 28px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 15px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .error-subtitle {
      font-size: 16px;
      color: #718096;
      margin-bottom: 30px;
      line-height: 1.5;
    }

    /* رسالة الخطأ */
    .error-message {
      background: linear-gradient(135deg, #fff5f5, #fed7d7);
      border: 2px solid #ff6b6b;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
    }

    .error-message::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .error-text {
      font-size: 16px;
      font-weight: 600;
      color: #c53030;
      margin: 0;
      line-height: 1.5;
    }

    /* معلومات إضافية */
    .error-details {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 30px;
      text-align: right;
    }

    .error-details h4 {
      color: #4a5568;
      font-size: 14px;
      margin-bottom: 10px;
      font-weight: 600;
    }

    .error-details ul {
      list-style: none;
      color: #718096;
      font-size: 13px;
      line-height: 1.6;
    }

    .error-details li {
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .error-details li::before {
      content: '•';
      color: #ff6b6b;
      font-weight: bold;
    }

    /* أزرار العمل */
    .action-buttons {
      display: flex;
      gap: 15px;
      flex-direction: column;
    }

    .return-button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 15px;
      font-size: 18px;
      font-weight: 700;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .return-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .return-button:hover::before {
      left: 100%;
    }

    .return-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .return-button:active {
      transform: translateY(0);
    }

    .return-icon {
      width: 20px;
      height: 20px;
      fill: white;
    }

    .retry-button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      color: #4a5568;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .retry-button:hover {
      background: linear-gradient(135deg, #edf2f7, #e2e8f0);
      border-color: #cbd5e0;
      transform: translateY(-1px);
    }

    .retry-icon {
      width: 18px;
      height: 18px;
      fill: currentColor;
    }

    /* تأثيرات إضافية */
    .floating-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 15%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 65%;
      right: 20%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 50px;
      height: 50px;
      bottom: 25%;
      left: 25%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .error-container {
        padding: 30px 25px;
        border-radius: 20px;
      }

      .error-title {
        font-size: 24px;
      }

      .error-subtitle {
        font-size: 14px;
      }

      .logo {
        width: 90px;
        height: 90px;
        margin-bottom: 20px;
      }

      .error-icon {
        width: 70px;
        height: 70px;
      }

      .error-icon svg {
        width: 35px;
        height: 35px;
      }

      .return-button {
        padding: 12px;
        font-size: 16px;
      }

      .action-buttons {
        gap: 12px;
      }
    }

    @media (max-width: 480px) {
      body {
        padding: 15px;
      }

      .error-container {
        padding: 25px 20px;
        border-radius: 18px;
      }

      .error-title {
        font-size: 20px;
      }

      .logo {
        width: 80px;
        height: 80px;
        margin-bottom: 18px;
      }

      .error-icon {
        width: 60px;
        height: 60px;
      }

      .error-icon svg {
        width: 30px;
        height: 30px;
      }

      .return-button {
        padding: 10px;
        font-size: 14px;
      }

      .retry-button {
        padding: 10px;
        font-size: 14px;
      }
    }

    /* تحسين الأداء */
    .gpu-accelerated {
      transform: translateZ(0);
      will-change: transform;
    }
  </style>
</head>
<body>
  <!-- أشكال متحركة في الخلفية -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="error-container gpu-accelerated">
    <img src="logo.png" alt="Flora Net" class="logo">

    <div class="error-icon">
      <svg viewBox="0 0 24 24">
        <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
      </svg>
    </div>

    <h1 class="error-title">حدث خطأ</h1>
    <p class="error-subtitle">عذراً، لم نتمكن من معالجة طلبك</p>

    <div class="error-message">
      <p class="error-text">$(error)</p>
    </div>

    <div class="error-details">
      <h4>الأسباب المحتملة:</h4>
      <ul>
        <li>خطأ في اسم المستخدم أو كلمة المرور</li>
        <li>انتهاء صلاحية الجلسة</li>
        <li>مشكلة في الاتصال بالشبكة</li>
        <li>صيانة مؤقتة في النظام</li>
      </ul>
    </div>

    <div class="action-buttons">
      <a class="return-button gpu-accelerated" href="$(link-login)">
        <svg class="return-icon" viewBox="0 0 24 24">
          <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
        </svg>
        العودة لتسجيل الدخول
      </a>

      <button class="retry-button gpu-accelerated" onclick="window.location.reload()">
        <svg class="retry-icon" viewBox="0 0 24 24">
          <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
        </svg>
        إعادة المحاولة
      </button>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // تأثير النقر على الشعار
      const logo = document.querySelector('.logo');
      if (logo) {
        logo.addEventListener('click', function() {
          this.style.animation = 'bounce 0.6s ease-out';
          setTimeout(() => {
            this.style.animation = '';
          }, 600);
        });
      }

      // تأثير النقر على أيقونة الخطأ
      const errorIcon = document.querySelector('.error-icon');
      errorIcon.addEventListener('click', function() {
        this.style.animation = 'errorShake 0.6s ease-out, bounce 0.6s ease-out';
        setTimeout(() => {
          this.style.animation = 'errorShake 2s ease-in-out infinite';
        }, 600);
      });

      // تأثيرات الأزرار
      const buttons = document.querySelectorAll('.return-button, .retry-button');
      buttons.forEach(button => {
        button.addEventListener('click', function(e) {
          // تأثير الموجة
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
          `;

          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // إضافة تأثيرات CSS
      const style = document.createElement('style');
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        @keyframes bounce {
          0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
          40% { transform: translateY(-10px) scale(1.1); }
          80% { transform: translateY(-5px) scale(1.05); }
        }
      `;
      document.head.appendChild(style);

      // تحسين الأداء
      window.addEventListener('load', function() {
        document.querySelectorAll('.gpu-accelerated').forEach(element => {
          element.style.transform = 'translateZ(0)';
        });
      });
    });
  </script>
</body>
</html>
