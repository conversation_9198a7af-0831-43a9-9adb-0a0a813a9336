<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flora Net - تسجيل الخروج</title>
  <style>
    /* تحميل خط الألمراي */
    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
    }

    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
    }

    /* إعادة تعيين الأنماط الأساسية */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* تدرج خلفية متحرك */
    body {
      font-family: 'Almarai', 'Tahoma', sans-serif;
      background: linear-gradient(-45deg, #48bb78, #38a169, #68d391, #9ae6b4);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow-x: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* الحاوية الرئيسية */
    .logout-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      padding: 40px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInUp 0.8s ease-out;
      position: relative;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
      margin: 0 auto;
      text-align: center;
    }

    .logout-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #48bb78, #38a169);
      border-radius: 25px 25px 0 0;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* أيقونة النجاح */
    .success-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 25px;
      background: linear-gradient(135deg, #48bb78, #38a169);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: successPulse 2s ease-in-out infinite;
      box-shadow: 0 10px 30px rgba(72, 187, 120, 0.3);
    }

    @keyframes successPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .success-icon svg {
      width: 40px;
      height: 40px;
      fill: white;
    }

    /* العنوان */
    .logout-title {
      font-size: 28px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 15px;
      background: linear-gradient(135deg, #48bb78, #38a169);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .logout-subtitle {
      font-size: 16px;
      color: #718096;
      margin-bottom: 30px;
      line-height: 1.5;
    }

    /* رسالة النجاح */
    .success-message {
      background: linear-gradient(135deg, #f0fff4, #e6fffa);
      border: 2px solid #48bb78;
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
    }

    .success-message::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(72, 187, 120, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .success-text {
      font-size: 18px;
      font-weight: 600;
      color: #2f855a;
      margin: 0;
    }

    /* زر العودة */
    .return-button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 15px;
      font-size: 18px;
      font-weight: 700;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .return-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .return-button:hover::before {
      left: 100%;
    }

    .return-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .return-button:active {
      transform: translateY(0);
    }

    .return-icon {
      width: 20px;
      height: 20px;
      fill: white;
    }

    /* تأثيرات إضافية */
    .floating-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 70px;
      height: 70px;
      top: 15%;
      left: 15%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 50px;
      height: 50px;
      top: 70%;
      right: 20%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 90px;
      height: 90px;
      bottom: 20%;
      left: 25%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-25px) rotate(180deg);
      }
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .logout-container {
        padding: 30px 25px;
        border-radius: 20px;
      }

      .logout-title {
        font-size: 24px;
      }

      .logout-subtitle {
        font-size: 14px;
      }

      .success-icon {
        width: 70px;
        height: 70px;
      }

      .success-icon svg {
        width: 35px;
        height: 35px;
      }

      .return-button {
        padding: 12px;
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      body {
        padding: 15px;
      }

      .logout-container {
        padding: 25px 20px;
        border-radius: 18px;
      }

      .logout-title {
        font-size: 20px;
      }

      .success-icon {
        width: 60px;
        height: 60px;
      }

      .success-icon svg {
        width: 30px;
        height: 30px;
      }

      .return-button {
        padding: 10px;
        font-size: 14px;
      }
    }

    /* تحسين الأداء */
    .gpu-accelerated {
      transform: translateZ(0);
      will-change: transform;
    }
  </style>
</head>
<body>
  <!-- أشكال متحركة في الخلفية -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="logout-container gpu-accelerated">
    <div class="success-icon">
      <svg viewBox="0 0 24 24">
        <path d="M9,20.42l-6.21-6.21l2.83-2.83L9,14.77l9.88-9.89l2.83,2.83L9,20.42z"/>
      </svg>
    </div>

    <h1 class="logout-title">تم تسجيل الخروج بنجاح</h1>
    <p class="logout-subtitle">شكراً لاستخدامك خدمة Flora Net</p>

    <div class="success-message">
      <p class="success-text">تم إنهاء جلسة الإنترنت بأمان</p>
    </div>

    <a class="return-button gpu-accelerated" href="$(link-login)">
      <svg class="return-icon" viewBox="0 0 24 24">
        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
      </svg>
      العودة لتسجيل الدخول
    </a>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // تأثير النقر على الأيقونة
      const successIcon = document.querySelector('.success-icon');
      successIcon.addEventListener('click', function() {
        this.style.animation = 'successPulse 0.6s ease-out, bounce 0.6s ease-out';
        setTimeout(() => {
          this.style.animation = 'successPulse 2s ease-in-out infinite';
        }, 600);
      });

      // تأثير زر العودة
      const returnButton = document.querySelector('.return-button');
      returnButton.addEventListener('click', function(e) {
        // تأثير الموجة
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s linear;
          pointer-events: none;
        `;

        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });

      // إضافة تأثيرات CSS
      const style = document.createElement('style');
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        @keyframes bounce {
          0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
          40% { transform: translateY(-10px) scale(1.1); }
          80% { transform: translateY(-5px) scale(1.05); }
        }
      `;
      document.head.appendChild(style);

      // تحسين الأداء
      window.addEventListener('load', function() {
        document.querySelectorAll('.gpu-accelerated').forEach(element => {
          element.style.transform = 'translateZ(0)';
        });
      });
    });
  </script>
</body>
</html>
