<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flora Net - تسجيل الدخول</title>
  <style>
    /* تحميل خط الألمراي */
    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
    }

    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
    }

    /* إعادة تعيين الأنماط الأساسية */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* تدرج خلفية متحرك */
    body {
      font-family: 'Almarai', 'Tahoma', sans-serif;
      background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow-x: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* الحاوية الرئيسية */
    .main-container {
      display: grid;
      grid-template-columns: 1fr;
      max-width: 1200px;
      width: 100%;
      gap: 30px;
      align-items: start;
    }

    /* تخطيط سطح المكتب */
    @media (min-width: 992px) {
      .main-container {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
      }
    }

    /* صندوق تسجيل الدخول */
    .login-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 25px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInUp 0.8s ease-out;
      position: relative;
      overflow: hidden;
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
    }

    @media (min-width: 768px) {
      .login-container {
        padding: 35px;
        border-radius: 25px;
      }
    }

    @media (min-width: 992px) {
      .login-container {
        padding: 40px;
        animation: slideInRight 0.8s ease-out;
        margin: 0;
      }
    }

    .login-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 25px 25px 0 0;
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* الشعار والعنوان */
    .logo-section {
      text-align: center;
      margin-bottom: 25px;
    }

    .logo {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-bottom: 20px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      border: 3px solid rgba(255, 255, 255, 0.8);
    }

    @media (min-width: 768px) {
      .logo {
        width: 120px;
        height: 120px;
        margin-bottom: 25px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        border-width: 4px;
      }

      .logo-section {
        margin-bottom: 35px;
      }
    }

    @media (min-width: 992px) {
      .logo {
        width: 140px;
        height: 140px;
        margin-bottom: 30px;
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
        border-width: 5px;
      }

      .logo-section {
        margin-bottom: 40px;
      }
    }

    .logo:hover {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 30px 70px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 1);
    }

    .welcome-title {
      font-size: 20px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 6px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      line-height: 1.3;
    }

    @media (min-width: 768px) {
      .welcome-title {
        font-size: 24px;
        margin-bottom: 7px;
      }
    }

    @media (min-width: 992px) {
      .welcome-title {
        font-size: 28px;
        margin-bottom: 8px;
      }
    }

    .welcome-subtitle {
      font-size: 14px;
      color: #718096;
      margin-bottom: 25px;
      line-height: 1.4;
    }

    @media (min-width: 768px) {
      .welcome-subtitle {
        font-size: 15px;
        margin-bottom: 28px;
      }
    }

    @media (min-width: 992px) {
      .welcome-subtitle {
        font-size: 16px;
        margin-bottom: 30px;
      }
    }

    /* رسالة الخطأ */
    .error {
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      font-size: 14px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
      animation: slideInError 0.5s ease-out;
      display: none;
      position: relative;
      overflow: hidden;
    }

    .error.show {
      display: block;
      animation: slideInError 0.5s ease-out;
    }

    .error::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shimmer 2s infinite;
    }

    @keyframes slideInError {
      0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    /* نموذج تسجيل الدخول */
    .login-form {
      margin-bottom: 25px;
    }

    @media (min-width: 768px) {
      .login-form {
        margin-bottom: 28px;
      }
    }

    @media (min-width: 992px) {
      .login-form {
        margin-bottom: 30px;
      }
    }

    .input-group {
      position: relative;
      margin-bottom: 20px;
    }

    @media (min-width: 768px) {
      .input-group {
        margin-bottom: 23px;
      }
    }

    @media (min-width: 992px) {
      .input-group {
        margin-bottom: 25px;
      }
    }

    .input-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      opacity: 0.6;
      transition: all 0.3s ease;
      pointer-events: none;
      fill: #a0aec0;
    }

    .input-group:focus-within .input-icon {
      opacity: 1;
      transform: translateY(-50%) scale(1.1);
      fill: #667eea;
    }

    @media (min-width: 768px) {
      .input-icon {
        width: 22px;
        height: 22px;
        right: 16px;
      }
    }

    @media (min-width: 992px) {
      .input-icon {
        width: 24px;
        height: 24px;
        right: 18px;
      }
    }

    .input-field {
      width: 100%;
      padding: 12px 45px 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      font-size: 14px;
      font-family: 'Almarai', sans-serif;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
    }

    @media (min-width: 768px) {
      .input-field {
        padding: 14px 50px 14px 18px;
        border-radius: 14px;
        font-size: 15px;
      }
    }

    @media (min-width: 992px) {
      .input-field {
        padding: 15px 55px 15px 20px;
        border-radius: 15px;
        font-size: 16px;
      }
    }

    .input-field:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
      background: rgba(255, 255, 255, 0.95);
    }

    .input-field::placeholder {
      color: #a0aec0;
      font-family: 'Almarai', sans-serif;
      transition: all 0.3s ease;
    }

    .input-field:focus::placeholder {
      opacity: 0.7;
      transform: translateX(5px);
    }

    /* تأثير التموج للحقول */
    .input-field {
      position: relative;
      overflow: hidden;
    }

    .input-field::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
      transition: left 0.6s;
    }

    .input-field:focus::before {
      left: 100%;
    }

    .login-button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 700;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    @media (min-width: 768px) {
      .login-button {
        padding: 14px;
        border-radius: 14px;
        font-size: 17px;
      }
    }

    @media (min-width: 992px) {
      .login-button {
        padding: 15px;
        border-radius: 15px;
        font-size: 18px;
      }
    }

    .login-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .login-button:hover::before {
      left: 100%;
    }

    .login-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .login-button:active {
      transform: translateY(0);
    }

    /* معلومات الباقات ونقاط البيع */
    .info-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      animation: slideInUp 0.8s ease-out;
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
    }

    @media (min-width: 768px) {
      .info-container {
        gap: 25px;
      }
    }

    @media (min-width: 992px) {
      .info-container {
        gap: 30px;
        animation: slideInLeft 0.8s ease-out;
        margin: 0;
      }
    }

    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .info-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 20px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: transform 0.3s ease;
    }

    @media (min-width: 768px) {
      .info-card {
        border-radius: 18px;
        padding: 25px;
        box-shadow: 0 18px 38px rgba(0, 0, 0, 0.1);
      }
    }

    @media (min-width: 992px) {
      .info-card {
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }
    }

    .info-card:hover {
      transform: translateY(-3px);
    }

    @media (min-width: 768px) {
      .info-card:hover {
        transform: translateY(-4px);
      }
    }

    @media (min-width: 992px) {
      .info-card:hover {
        transform: translateY(-5px);
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      line-height: 1.3;
    }

    @media (min-width: 768px) {
      .card-title {
        font-size: 20px;
        margin-bottom: 18px;
        gap: 9px;
      }
    }

    @media (min-width: 992px) {
      .card-title {
        font-size: 22px;
        margin-bottom: 20px;
        gap: 10px;
      }
    }

    .card-icon {
      width: 24px;
      height: 24px;
      transition: all 0.3s ease;
    }

    @media (min-width: 768px) {
      .card-icon {
        width: 26px;
        height: 26px;
      }
    }

    @media (min-width: 992px) {
      .card-icon {
        width: 28px;
        height: 28px;
      }
    }

    .card-icon svg {
      width: 100%;
      height: 100%;
      fill: #667eea;
      transition: all 0.3s ease;
    }

    .info-card:hover .card-icon svg {
      transform: scale(1.1) rotate(5deg);
      fill: #764ba2;
    }

    /* بطاقات الباقات */
    .package-grid {
      display: grid;
      gap: 12px;
    }

    @media (min-width: 768px) {
      .package-grid {
        gap: 14px;
      }
    }

    @media (min-width: 992px) {
      .package-grid {
        gap: 15px;
      }
    }

    .package-item {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      padding: 16px;
      border-radius: 12px;
      border-right: 3px solid #667eea;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    @media (min-width: 768px) {
      .package-item {
        padding: 18px;
        border-radius: 14px;
        border-right-width: 3.5px;
      }
    }

    @media (min-width: 992px) {
      .package-item {
        padding: 20px;
        border-radius: 15px;
        border-right-width: 4px;
      }
    }

    .package-item:hover {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      transform: translateX(-3px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
    }

    @media (min-width: 768px) {
      .package-item:hover {
        transform: translateX(-4px);
        box-shadow: 0 9px 23px rgba(102, 126, 234, 0.2);
      }
    }

    @media (min-width: 992px) {
      .package-item:hover {
        transform: translateX(-5px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
      }
    }

    .package-name {
      font-weight: 700;
      font-size: 14px;
      margin-bottom: 4px;
      line-height: 1.3;
    }

    @media (min-width: 768px) {
      .package-name {
        font-size: 15px;
        margin-bottom: 4.5px;
      }
    }

    @media (min-width: 992px) {
      .package-name {
        font-size: 16px;
        margin-bottom: 5px;
      }
    }

    .package-price {
      font-size: 12px;
      opacity: 0.8;
      line-height: 1.3;
    }

    @media (min-width: 768px) {
      .package-price {
        font-size: 13px;
      }
    }

    @media (min-width: 992px) {
      .package-price {
        font-size: 14px;
      }
    }

    /* قائمة نقاط البيع */
    .pos-list {
      list-style: none;
    }

    .pos-item {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      padding: 12px 16px;
      border-radius: 10px;
      margin-bottom: 10px;
      border-right: 3px solid #f093fb;
      transition: all 0.3s ease;
      cursor: pointer;
      font-size: 14px;
      line-height: 1.4;
    }

    @media (min-width: 768px) {
      .pos-item {
        padding: 14px 18px;
        border-radius: 11px;
        margin-bottom: 11px;
        border-right-width: 3.5px;
        font-size: 14.5px;
      }
    }

    @media (min-width: 992px) {
      .pos-item {
        padding: 15px 20px;
        border-radius: 12px;
        margin-bottom: 12px;
        border-right-width: 4px;
        font-size: 15px;
      }
    }

    .pos-item:hover {
      background: linear-gradient(135deg, #f093fb, #f5576c);
      color: white;
      transform: translateX(-3px);
      box-shadow: 0 6px 16px rgba(240, 147, 251, 0.2);
    }

    @media (min-width: 768px) {
      .pos-item:hover {
        transform: translateX(-4px);
        box-shadow: 0 7px 18px rgba(240, 147, 251, 0.2);
      }
    }

    @media (min-width: 992px) {
      .pos-item:hover {
        transform: translateX(-5px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.2);
      }
    }

    .pos-item:last-child {
      margin-bottom: 0;
    }

    /* تحسينات إضافية للشاشات الصغيرة */
    @media (max-width: 480px) {
      body {
        padding: 15px;
      }

      .main-container {
        gap: 15px;
      }

      .login-container,
      .info-card {
        border-radius: 15px;
      }

      .card-title {
        font-size: 16px;
        margin-bottom: 14px;
      }

      .card-icon {
        font-size: 20px;
      }
    }

    @media (max-width: 360px) {
      body {
        padding: 10px;
      }

      .main-container {
        gap: 12px;
      }

      .login-container {
        padding: 18px;
      }

      .info-card {
        padding: 16px;
      }

      .welcome-title {
        font-size: 18px;
      }

      .welcome-subtitle {
        font-size: 13px;
      }

      .input-field {
        padding: 10px 14px;
        font-size: 13px;
      }

      .login-button {
        padding: 10px;
        font-size: 14px;
      }
    }

    /* تأثيرات إضافية */
    .floating-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 50px;
      height: 50px;
      top: 15%;
      left: 8%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 40px;
      height: 40px;
      top: 55%;
      right: 8%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 60px;
      height: 60px;
      bottom: 15%;
      left: 15%;
      animation-delay: 4s;
    }

    @media (min-width: 768px) {
      .shape {
        background: rgba(255, 255, 255, 0.1);
      }

      .shape:nth-child(1) {
        width: 65px;
        height: 65px;
        top: 18%;
        left: 9%;
      }

      .shape:nth-child(2) {
        width: 50px;
        height: 50px;
        top: 58%;
        right: 9%;
      }

      .shape:nth-child(3) {
        width: 80px;
        height: 80px;
        bottom: 18%;
        left: 18%;
      }
    }

    @media (min-width: 992px) {
      .shape:nth-child(1) {
        width: 80px;
        height: 80px;
        top: 20%;
        left: 10%;
      }

      .shape:nth-child(2) {
        width: 60px;
        height: 60px;
        top: 60%;
        right: 10%;
      }

      .shape:nth-child(3) {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
      }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-15px) rotate(180deg);
      }
    }

    @media (min-width: 768px) {
      @keyframes float {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-18px) rotate(180deg);
        }
      }
    }

    @media (min-width: 992px) {
      @keyframes float {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }
    }

    /* تأثيرات إضافية للتفاعل */
    .pulse {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .bounce-in {
      animation: bounceIn 0.6s ease-out;
    }

    @keyframes bounceIn {
      0% { transform: scale(0.3); opacity: 0; }
      50% { transform: scale(1.05); }
      70% { transform: scale(0.9); }
      100% { transform: scale(1); opacity: 1; }
    }

    /* تأثير الضوء المتحرك */
    .light-effect {
      position: relative;
      overflow: hidden;
    }

    .light-effect::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.8s;
    }

    .light-effect:hover::after {
      left: 100%;
    }

    /* تحسين الأداء */
    .gpu-accelerated {
      transform: translateZ(0);
      will-change: transform;
    }
  </style>
</head>
<body>

  <!-- أشكال متحركة في الخلفية -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="main-container">
    <!-- قسم تسجيل الدخول -->
    <div class="login-container">
      <div class="logo-section">
        <img src="logo.png" alt="Flora Net" class="logo">
        <h1 class="welcome-title">مرحباً بك في Flora Net</h1>
        <p class="welcome-subtitle">سجل دخولك للاستمتاع بخدمة الإنترنت</p>
      </div>

      <!-- رسالة الخطأ -->
      <div class="error" id="errorMessage">$(error)</div>

      <!-- نموذج تسجيل الدخول -->
      <form name="login" action="$(link-login-only)" method="post" class="login-form" id="loginForm">
        <input type="hidden" name="dst" value="$(link-orig)">
        <input type="hidden" name="popup" value="true">

        <div class="input-group">
          <svg class="input-icon" viewBox="0 0 24 24">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
          <input type="text" name="username" class="input-field gpu-accelerated" placeholder="اسم المستخدم" required>
        </div>

        <div class="input-group">
          <svg class="input-icon" viewBox="0 0 24 24">
            <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z"/>
          </svg>
          <input type="password" name="password" class="input-field gpu-accelerated" placeholder="كلمة المرور" required>
        </div>

        <button type="submit" class="login-button light-effect gpu-accelerated">
          <svg style="width: 20px; height: 20px; margin-left: 8px; display: inline-block; vertical-align: middle; fill: white;" viewBox="0 0 24 24">
            <path d="M1,9l2,2c4.97-4.97,13.03-4.97,18,0l2-2C16.93,2.93,7.07,2.93,1,9z M9,17l3,3l3-3c-1.65-1.66-4.34-1.66-6,0z M5,13l2,2c2.76-2.76,7.24-2.76,10,0l2-2C15.14,9.14,8.87,9.14,5,13z"/>
          </svg>
          تسجيل الدخول
        </button>
      </form>
    </div>

    <!-- قسم المعلومات -->
    <div class="info-container">
      <!-- أسعار الباقات -->
      <div class="info-card light-effect">
        <h3 class="card-title">
          <span class="card-icon">
            <svg viewBox="0 0 24 24">
              <path d="M19,7h-3V6a4,4,0,0,0-8,0V7H5A1,1,0,0,0,4,8V19a3,3,0,0,0,3,3H17a3,3,0,0,0,3-3V8A1,1,0,0,0,19,7ZM10,6a2,2,0,0,1,4,0V7H10Zm8,13a1,1,0,0,1-1,1H7a1,1,0,0,1-1-1V9H8v1a1,1,0,0,0,2,0V9h4v1a1,1,0,0,0,2,0V9h2Z"/>
            </svg>
          </span>
          باقات الإنترنت المتاحة
        </h3>
        <div class="package-grid">
          <div class="package-item gpu-accelerated" data-package="daily">
            <div class="package-name">3 شيكل </div>
            <div class="package-price">سرعة ٢ ميغا ٢٤ ساعة</div>
          </div>
          <div class="package-item gpu-accelerated" data-package="weekly">
            <div class="package-name">2 شيكل</div>
            <div class="package-price">سرعة ٣ ميغا ١٠ ساعات</div>
          </div>
          <div class="package-item gpu-accelerated" data-package="monthly">
            <div class="package-name">2 شيكل </div>
            <div class="package-price">سرعة ٥ ميغا ٢ ساعة</div>
          </div>
        </div>
      </div>

      <!-- نقاط البيع -->
      <div class="info-card light-effect">
        <h3 class="card-title">
          <span class="card-icon">
            <svg viewBox="0 0 24 24">
              <path d="M12,2C8.13,2,5,5.13,5,9c0,5.25,7,13,7,13s7-7.75,7-13C19,5.13,15.87,2,12,2z M12,11.5c-1.38,0-2.5-1.12-2.5-2.5s1.12-2.5,2.5-2.5s2.5,1.12,2.5,2.5S13.38,11.5,12,11.5z"/>
            </svg>
          </span>
          نقاط البيع 
        </h3>
        <ul class="pos-list">
          <li class="pos-item gpu-accelerated">
            <svg style="width: 16px; height: 16px; margin-left: 8px; display: inline-block; vertical-align: middle; fill: currentColor;" viewBox="0 0 24 24">
              <path d="M12,2C8.13,2,5,5.13,5,9c0,5.25,7,13,7,13s7-7.75,7-13C19,5.13,15.87,2,12,2z M12,11.5c-1.38,0-2.5-1.12-2.5-2.5s1.12-2.5,2.5-2.5s2.5,1.12,2.5,2.5S13.38,11.5,12,11.5z"/>
            </svg>
            خلف فيلا أبو عقلين
          </li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // تأثيرات تفاعلية محسنة
    document.addEventListener('DOMContentLoaded', function() {
      // إخفاء رسالة الخطأ بشكل افتراضي
      const errorMessage = document.getElementById('errorMessage');
      const errorContent = errorMessage.textContent.trim();

      if (!errorContent || errorContent === '$(error)') {
        errorMessage.style.display = 'none';
      } else {
        errorMessage.classList.add('show');
      }

      // تأثيرات حقول الإدخال المحسنة
      const inputs = document.querySelectorAll('.input-field');
      inputs.forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
          this.parentElement.style.transform = 'scale(1.02)';
          this.parentElement.classList.add('pulse');
        });

        input.addEventListener('blur', function() {
          this.parentElement.style.transform = 'scale(1)';
          this.parentElement.classList.remove('pulse');
        });

        // تأثير الكتابة
        input.addEventListener('input', function() {
          if (this.value.length > 0) {
            this.style.borderColor = '#48bb78';
            this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
          } else {
            this.style.borderColor = '#e2e8f0';
            this.style.boxShadow = 'none';
          }
        });
      });

      // تأثيرات زر تسجيل الدخول المحسنة
      const loginButton = document.querySelector('.login-button');
      const loginForm = document.getElementById('loginForm');

      loginButton.addEventListener('click', function(e) {
        // تأثير الموجة المحسن
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s linear;
          pointer-events: none;
        `;

        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });

      // تحقق من صحة النموذج
      loginForm.addEventListener('submit', function(e) {
        const username = this.querySelector('input[name="username"]').value;
        const password = this.querySelector('input[name="password"]').value;

        if (!username || !password) {
          e.preventDefault();
          showError('يرجى ملء جميع الحقول المطلوبة');
          return;
        }

        // تأثير التحميل
        loginButton.innerHTML = `
          <div style="display: inline-flex; align-items: center;">
            <div style="width: 20px; height: 20px; border: 2px solid #ffffff; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite; margin-left: 8px;"></div>
            جاري تسجيل الدخول...
          </div>
        `;
        loginButton.disabled = true;
      });

      // تأثيرات بطاقات الباقات
      const packageItems = document.querySelectorAll('.package-item');
      packageItems.forEach(item => {
        item.addEventListener('click', function() {
          // إزالة التحديد من جميع البطاقات
          packageItems.forEach(p => p.classList.remove('selected'));

          // تحديد البطاقة المختارة
          this.classList.add('selected');
          this.classList.add('bounce-in');

          // إظهار رسالة تأكيد
          showNotification(`تم اختيار ${this.querySelector('.package-name').textContent}`);

          setTimeout(() => {
            this.classList.remove('bounce-in');
          }, 600);
        });
      });

      // تأثيرات نقاط البيع
      const posItems = document.querySelectorAll('.pos-item');
      posItems.forEach(item => {
        item.addEventListener('click', function() {
          this.classList.add('pulse');
          showNotification('تم نسخ عنوان نقطة البيع');

          setTimeout(() => {
            this.classList.remove('pulse');
          }, 2000);
        });
      });

      // تأثيرات الشعار
      const logo = document.querySelector('.logo');
      if (logo) {
        logo.addEventListener('click', function() {
          this.classList.add('bounce-in');
          setTimeout(() => {
            this.classList.remove('bounce-in');
          }, 600);
        });
      }

      // إضافة تأثيرات CSS للرسوم المتحركة
      const style = document.createElement('style');
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .package-item.selected {
          background: linear-gradient(135deg, #48bb78, #38a169) !important;
          color: white !important;
          transform: translateX(-5px) scale(1.02) !important;
          box-shadow: 0 15px 35px rgba(72, 187, 120, 0.3) !important;
        }

        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #48bb78, #38a169);
          color: white;
          padding: 12px 20px;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
          z-index: 1000;
          animation: slideInNotification 0.5s ease-out;
          font-family: 'Almarai', sans-serif;
        }

        @keyframes slideInNotification {
          from {
            opacity: 0;
            transform: translateX(100px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `;
      document.head.appendChild(style);
    });

    // دالة إظهار الأخطاء
    function showError(message) {
      const errorMessage = document.getElementById('errorMessage');
      errorMessage.textContent = message;
      errorMessage.classList.add('show');

      setTimeout(() => {
        errorMessage.classList.remove('show');
      }, 5000);
    }

    // دالة إظهار الإشعارات
    function showNotification(message) {
      const notification = document.createElement('div');
      notification.className = 'notification';
      notification.textContent = message;

      document.body.appendChild(notification);

      setTimeout(() => {
        notification.style.animation = 'slideInNotification 0.5s ease-out reverse';
        setTimeout(() => {
          notification.remove();
        }, 500);
      }, 3000);
    }

    // تحسين الأداء - تأخير تحميل التأثيرات الثقيلة
    window.addEventListener('load', function() {
      // تفعيل التأثيرات المتقدمة بعد تحميل الصفحة
      document.querySelectorAll('.gpu-accelerated').forEach(element => {
        element.style.transform = 'translateZ(0)';
      });
    });
  </script>
</body>
</html>
