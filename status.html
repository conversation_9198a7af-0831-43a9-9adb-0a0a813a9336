<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flora Net - حالة الاتصال</title>
  <style>
    /* تحميل خط الألمراي */
    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
    }

    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
    }

    /* إعادة تعيين الأنماط الأساسية */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* تدرج خلفية متحرك */
    body {
      font-family: 'Almarai', 'Tahoma', sans-serif;
      background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow-x: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* الحاوية الرئيسية */
    .status-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      padding: 40px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInUp 0.8s ease-out;
      position: relative;
      overflow: hidden;
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
    }

    .status-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #48bb78, #38a169);
      border-radius: 25px 25px 0 0;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* العنوان والشعار */
    .header-section {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-bottom: 25px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      border: 3px solid rgba(255, 255, 255, 0.8);
    }

    @media (min-width: 768px) {
      .logo {
        width: 120px;
        height: 120px;
        margin-bottom: 30px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        border-width: 4px;
      }
    }

    @media (min-width: 992px) {
      .logo {
        width: 140px;
        height: 140px;
        margin-bottom: 35px;
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
        border-width: 5px;
      }
    }

    .logo:hover {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 30px 70px rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 1);
    }

    .status-title {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 8px;
      background: linear-gradient(135deg, #48bb78, #38a169);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .status-icon {
      width: 28px;
      height: 28px;
      fill: #48bb78;
    }

    .status-subtitle {
      font-size: 16px;
      color: #718096;
      margin-bottom: 30px;
    }

    /* معلومات الحالة */
    .status-info {
      margin-bottom: 30px;
    }

    .info-item {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      padding: 18px 20px;
      border-radius: 15px;
      margin-bottom: 15px;
      border-right: 4px solid #48bb78;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
    }

    .info-item:hover {
      background: linear-gradient(135deg, #48bb78, #38a169);
      color: white;
      transform: translateX(-5px);
      box-shadow: 0 10px 25px rgba(72, 187, 120, 0.2);
    }

    .info-item:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .info-value {
      font-weight: 700;
      font-size: 16px;
    }

    .info-item-icon {
      width: 20px;
      height: 20px;
      fill: currentColor;
    }

    /* زر تسجيل الخروج */
    .logout-button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #f093fb, #f5576c);
      color: white;
      border: none;
      border-radius: 15px;
      font-size: 18px;
      font-weight: 700;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .logout-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .logout-button:hover::before {
      left: 100%;
    }

    .logout-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
    }

    .logout-button:active {
      transform: translateY(0);
    }

    .logout-icon {
      width: 20px;
      height: 20px;
      fill: white;
    }

    /* تأثيرات إضافية */
    .floating-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 50px;
      height: 50px;
      bottom: 25%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .status-container {
        padding: 30px 25px;
        border-radius: 20px;
      }

      .status-title {
        font-size: 20px;
      }

      .status-subtitle {
        font-size: 14px;
      }

      .info-item {
        padding: 15px 18px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .logout-button {
        padding: 12px;
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      body {
        padding: 15px;
      }

      .status-container {
        padding: 25px 20px;
        border-radius: 18px;
      }

      .status-title {
        font-size: 18px;
      }

      .info-item {
        padding: 12px 15px;
      }

      .logout-button {
        padding: 10px;
        font-size: 14px;
      }
    }

    /* تحسين الأداء */
    .gpu-accelerated {
      transform: translateZ(0);
      will-change: transform;
    }
  </style>
</head>
<body>
  <!-- أشكال متحركة في الخلفية -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="status-container gpu-accelerated">
    <div class="header-section">
      <img src="logo.png" alt="Flora Net" class="logo">
      <h1 class="status-title">
        <svg class="status-icon" viewBox="0 0 24 24">
          <path d="M1,9l2,2c4.97-4.97,13.03-4.97,18,0l2-2C16.93,2.93,7.07,2.93,1,9z M9,17l3,3l3-3c-1.65-1.66-4.34-1.66-6,0z M5,13l2,2c2.76-2.76,7.24-2.76,10,0l2-2C15.14,9.14,8.87,9.14,5,13z"/>
        </svg>
        حالة الاتصال
      </h1>
      <p class="status-subtitle">معلومات جلسة الإنترنت الحالية</p>
    </div>

    <div class="status-info">
      <div class="info-item gpu-accelerated">
        <div class="info-label">
          <svg class="info-item-icon" viewBox="0 0 24 24">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
          اسم المستخدم
        </div>
        <div class="info-value">$(username)</div>
      </div>

      <div class="info-item gpu-accelerated">
        <div class="info-label">
          <svg class="info-item-icon" viewBox="0 0 24 24">
            <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
          </svg>
          مدة الاتصال
        </div>
        <div class="info-value">$(uptime)</div>
      </div>

      <div class="info-item gpu-accelerated">
        <div class="info-label">
          <svg class="info-item-icon" viewBox="0 0 24 24">
            <path d="M15,1H9v2h6V1z M11,14h2V8h-2V14z M19.03,7.39l1.42-1.42c-0.43-0.51-0.9-0.99-1.41-1.41l-1.42,1.42 C16.07,4.74,14.12,4,12,4c-4.97,0-9,4.03-9,9s4.02,9,9,9s9-4.03,9-9C21,10.88,20.26,8.93,19.03,7.39z M12,20c-3.87,0-7-3.13-7-7 s3.13-7,7-7s7,3.13,7,7S15.87,20,12,20z"/>
          </svg>
          الوقت المتبقي
        </div>
        <div class="info-value">$(session-time-left)</div>
      </div>

      <div class="info-item gpu-accelerated">
        <div class="info-label">
          <svg class="info-item-icon" viewBox="0 0 24 24">
            <path d="M19,7h-3V6a4,4,0,0,0-8,0V7H5A1,1,0,0,0,4,8V19a3,3,0,0,0,3,3H17a3,3,0,0,0,3-3V8A1,1,0,0,0,19,7ZM10,6a2,2,0,0,1,4,0V7H10Zm8,13a1,1,0,0,1-1,1H7a1,1,0,0,1-1-1V9H8v1a1,1,0,0,0,2,0V9h4v1a1,1,0,0,0,2,0V9h2Z"/>
          </svg>
          نوع الباقة
        </div>
        <div class="info-value">$(profile)</div>
      </div>

      <div class="info-item gpu-accelerated">
        <div class="info-label">
          <svg class="info-item-icon" viewBox="0 0 24 24">
            <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M11,19.93c-3.94-0.49-7-3.85-7-7.93 c0-0.62,0.08-1.21,0.21-1.79L9,15v1c0,1.1,0.9,2,2,2V19.93z M17.9,17.39c-0.26-0.81-1-1.39-1.9-1.39h-1v-3c0-0.55-0.45-1-1-1H8v-2h2 c0.55,0,1-0.45,1-1V7h2c1.1,0,2-0.9,2-2v-0.41C17.92,5.77,20,8.65,20,12C20,14.08,19.2,15.97,17.9,17.39z"/>
          </svg>
          عنوان IP
        </div>
        <div class="info-value">$(ip)</div>
      </div>
    </div>

    <a class="logout-button gpu-accelerated" href="$(link-logout)">
      <svg class="logout-icon" viewBox="0 0 24 24">
        <path d="M17,7l-1.41,1.41L18.17,11H8v2h10.17l-2.58,2.58L17,17l5-5L17,7z M4,5h8V3H4C2.9,3,2,3.9,2,5v14c0,1.1,0.9,2,2,2h8v-2H4V5z"/>
      </svg>
      تسجيل الخروج
    </a>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // تأثيرات تفاعلية للعناصر
      const infoItems = document.querySelectorAll('.info-item');
      infoItems.forEach(item => {
        item.addEventListener('click', function() {
          this.style.animation = 'pulse 0.6s ease-out';
          setTimeout(() => {
            this.style.animation = '';
          }, 600);
        });
      });

      // تأثير الشعار
      const logo = document.querySelector('.logo');
      if (logo) {
        logo.addEventListener('click', function() {
          this.style.animation = 'bounce 0.6s ease-out';
          setTimeout(() => {
            this.style.animation = '';
          }, 600);
        });
      }

      // إضافة تأثيرات CSS
      const style = document.createElement('style');
      style.textContent = `
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.02); }
          100% { transform: scale(1); }
        }

        @keyframes bounce {
          0%, 20%, 60%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          80% { transform: translateY(-5px); }
        }
      `;
      document.head.appendChild(style);

      // تحسين الأداء
      window.addEventListener('load', function() {
        document.querySelectorAll('.gpu-accelerated').forEach(element => {
          element.style.transform = 'translateZ(0)';
        });
      });
    });
  </script>
</body>
</html>
