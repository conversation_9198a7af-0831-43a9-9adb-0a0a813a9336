<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flora Net - تسجيل الدخول</title>
  <style>
    /* تحميل خط الألمراي */
    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Regular.ttf') format('truetype');
      font-weight: 400;
      font-style: normal;
    }

    @font-face {
      font-family: 'Almarai';
      src: url('fonts/Almarai-Bold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
    }

    /* إعادة تعيين الأنماط الأساسية */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* تدرج خلفية متحرك */
    body {
      font-family: 'Almarai', 'Tahoma', sans-serif;
      background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow-x: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* الحاوية الرئيسية */
    .main-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      max-width: 1200px;
      width: 100%;
      gap: 40px;
      align-items: start;
    }

    /* صندوق تسجيل الدخول */
    .login-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 25px;
      padding: 40px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: slideInRight 0.8s ease-out;
      position: relative;
      overflow: hidden;
    }

    .login-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 25px 25px 0 0;
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    /* الشعار والعنوان */
    .logo-section {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-bottom: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .logo:hover {
      transform: scale(1.1) rotate(5deg);
    }

    .welcome-title {
      font-size: 28px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 8px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .welcome-subtitle {
      font-size: 16px;
      color: #718096;
      margin-bottom: 30px;
    }

    /* رسالة الخطأ */
    .error {
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      font-size: 14px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
      animation: shake 0.5s ease-in-out;
      display: none;
    }

    .error:not(:empty) {
      display: block;
    }

    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    /* نموذج تسجيل الدخول */
    .login-form {
      margin-bottom: 30px;
    }

    .input-group {
      position: relative;
      margin-bottom: 25px;
    }

    .input-field {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid #e2e8f0;
      border-radius: 15px;
      font-size: 16px;
      font-family: 'Almarai', sans-serif;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.8);
    }

    .input-field:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-2px);
    }

    .input-field::placeholder {
      color: #a0aec0;
      font-family: 'Almarai', sans-serif;
    }

    .login-button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 15px;
      font-size: 18px;
      font-weight: 700;
      font-family: 'Almarai', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .login-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .login-button:hover::before {
      left: 100%;
    }

    .login-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .login-button:active {
      transform: translateY(0);
    }

    /* معلومات الباقات ونقاط البيع */
    .info-container {
      display: flex;
      flex-direction: column;
      gap: 30px;
      animation: slideInLeft 0.8s ease-out;
    }

    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .info-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: transform 0.3s ease;
    }

    .info-card:hover {
      transform: translateY(-5px);
    }

    .card-title {
      font-size: 22px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-icon {
      font-size: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* بطاقات الباقات */
    .package-grid {
      display: grid;
      gap: 15px;
    }

    .package-item {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      padding: 20px;
      border-radius: 15px;
      border-right: 4px solid #667eea;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .package-item:hover {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      transform: translateX(-5px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
    }

    .package-name {
      font-weight: 700;
      font-size: 16px;
      margin-bottom: 5px;
    }

    .package-price {
      font-size: 14px;
      opacity: 0.8;
    }

    /* قائمة نقاط البيع */
    .pos-list {
      list-style: none;
    }

    .pos-item {
      background: linear-gradient(135deg, #f7fafc, #edf2f7);
      padding: 15px 20px;
      border-radius: 12px;
      margin-bottom: 12px;
      border-right: 4px solid #f093fb;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .pos-item:hover {
      background: linear-gradient(135deg, #f093fb, #f5576c);
      color: white;
      transform: translateX(-5px);
      box-shadow: 0 8px 20px rgba(240, 147, 251, 0.2);
    }

    .pos-item:last-child {
      margin-bottom: 0;
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .main-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 10px;
      }

      .login-container,
      .info-card {
        padding: 25px;
      }

      .welcome-title {
        font-size: 24px;
      }

      body {
        padding: 10px;
      }
    }

    @media (max-width: 480px) {
      .login-container,
      .info-card {
        padding: 20px;
        border-radius: 15px;
      }

      .welcome-title {
        font-size: 20px;
      }

      .input-field,
      .login-button {
        padding: 12px 15px;
        font-size: 14px;
      }
    }

    /* تأثيرات إضافية */
    .floating-shapes {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 60px;
      height: 60px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }
  </style>
</head>
<body>
  <!-- أشكال متحركة في الخلفية -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="main-container">
    <!-- قسم تسجيل الدخول -->
    <div class="login-container">
      <div class="logo-section">
        <img src="logo.png" alt="Flora Net" class="logo">
        <h1 class="welcome-title">مرحباً بك في Flora Net</h1>
        <p class="welcome-subtitle">سجل دخولك للاستمتاع بخدمة الإنترنت</p>
      </div>

      <!-- رسالة الخطأ -->
      <div class="error">$(error)</div>

      <!-- نموذج تسجيل الدخول -->
      <form name="login" action="$(link-login-only)" method="post" class="login-form">
        <input type="hidden" name="dst" value="$(link-orig)">
        <input type="hidden" name="popup" value="true">

        <div class="input-group">
          <input type="text" name="username" class="input-field" placeholder="اسم المستخدم" required>
        </div>

        <div class="input-group">
          <input type="password" name="password" class="input-field" placeholder="كلمة المرور" required>
        </div>

        <button type="submit" class="login-button">تسجيل الدخول</button>
      </form>
    </div>

    <!-- قسم المعلومات -->
    <div class="info-container">
      <!-- أسعار الباقات -->
      <div class="info-card">
        <h3 class="card-title">
          <span class="card-icon">💳</span>
          باقات الإنترنت المتاحة
        </h3>
        <div class="package-grid">
          <div class="package-item">
            <div class="package-name">باقة يومية</div>
            <div class="package-price">24 ساعة - 1 دولار</div>
          </div>
          <div class="package-item">
            <div class="package-name">باقة أسبوعية</div>
            <div class="package-price">7 أيام - 5 دولار</div>
          </div>
          <div class="package-item">
            <div class="package-name">باقة شهرية</div>
            <div class="package-price">30 يوم - 15 دولار</div>
          </div>
        </div>
      </div>

      <!-- نقاط البيع -->
      <div class="info-card">
        <h3 class="card-title">
          <span class="card-icon">📍</span>
          نقاط البيع والشحن
        </h3>
        <ul class="pos-list">
          <li class="pos-item">المتجر الرئيسي - وسط المدينة</li>
          <li class="pos-item">مكتبة السلام - شارع النصر</li>
          <li class="pos-item">محطة بترول الغد - الطريق العام</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // تأثيرات تفاعلية إضافية
    document.addEventListener('DOMContentLoaded', function() {
      // تأثير التركيز على حقول الإدخال
      const inputs = document.querySelectorAll('.input-field');
      inputs.forEach(input => {
        input.addEventListener('focus', function() {
          this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
          this.parentElement.style.transform = 'scale(1)';
        });
      });

      // تأثير النقر على زر تسجيل الدخول
      const loginButton = document.querySelector('.login-button');
      loginButton.addEventListener('click', function(e) {
        // إضافة تأثير الموجة
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });
  </script>
</body>
</html>
